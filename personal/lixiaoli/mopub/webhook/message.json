{"type": "message", "attachments": [{"contentType": "application/vnd.microsoft.card.adaptive", "content": {"$schema": "http://adaptivecards.io/schemas/adaptive-card.json", "type": "AdaptiveCard", "version": "1.2", "body": [{"type": "TextBlock", "text": "[Test Webhook]Hi <at><PERSON><PERSON></at>, this is a test message.", "wrap": true}], "msteams": {"entities": [{"type": "mention", "text": "<at><PERSON><PERSON></at>", "mentioned": {"id": "<EMAIL>", "name": "<PERSON><PERSON>"}}]}}}]}