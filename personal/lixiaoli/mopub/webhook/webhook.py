import requests
import json


def main():
    # Read JSON file content directly as string
    with open("message.json", "r", encoding="utf-8") as file:
        payload_json = file.read()

    url = "https://microsoft.webhook.office.com/webhookb2/5eaa2376-6b7c-44a3-a917-cd16440e9cfa@72f988bf-86f1-41af-91ab-2d7cd011db47/IncomingWebhook/338babedd8654b7c9f71ce9855b32ab6/75c102a9-e447-4afc-a9ef-f34e34a4cd9b/V2fKsrx64-tknuaL8mFqGJklEbTy-OBJQRnXuzWReYbmk1"

    response = requests.post(url, data=payload_json, headers={"Content-Type": "application/json"})

    # Print response status for debugging
    print(f"Response status code: {response.status_code}")
    if response.status_code == 200:
        print("Message sent successfully!")
    else:
        print(f"Failed to send message: {response.text}")


if __name__ == "__main__":
    main()
