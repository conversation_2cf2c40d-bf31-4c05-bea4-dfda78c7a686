# backup the original rcall config
cp ~/.rcall_config.py ~/.rcall_config.py.bak

# add extra setup to rcall config
sed -i '' '/""" + EXTRA_SETUP/{
r /dev/stdin
d
}' ~/.rcall_config.py <<'EOF'
""" + EXTRA_SETUP + """
case "$HOSTNAME" in
*-0)
echo "yes" > ~/is_head_pod.txt

nohup bash -c '
  python /root/code/glass/microsoft/tools/convert/prepare_snapshot.py \
    --model_name="swe-agent-mini-2" \
    --falcon_path="'"$1"'" \
    --export_dir="'"$2"'" \
    > prepare_snapshot.log 2>&1 && \
  python /root/code/glass/personal/lixiaoli/mopub/teams.py \
    "$(tail -n 1 prepare_snapshot.log)"
' > mopub.log 2>&1 & ;;

*)
echo "no" > ~/is_head_pod.txt ;;
esac
"""
EOF

# create devbox
job_name="lixiaoli-ptuc-0723"
cluster="prod-southcentralus-hpe-3"
priority_class="team-critical"

BRIX_QUOTA=team-moonfire-genaicore OAIPKG_OVERRIDE_WHEEL=unsafe_skip \
    twdev create-ray-devbox \
    cluster=$cluster \
    num_pods=1 \
    setup_twapi=True \
    num_gpu=8 \
    job_name=$job_name \
    priority_class=$priority_class

echo "Job $job_name submitted"

# recover the original rcall config
mv ~/.rcall_config.py.bak ~/.rcall_config.py